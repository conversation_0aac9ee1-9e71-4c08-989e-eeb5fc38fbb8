# Dockerfile otimizado para Google Cloud Run e monorepo
FROM node:22-alpine AS base

# Instalar dependências do sistema
RUN apk add --no-cache libc6-compat

# Configurar pnpm
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

WORKDIR /app

# Stage 1: Instalação de dependências
FROM base AS deps

# Copiar arquivos de configuração do monorepo
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./
COPY package.json ./
COPY turbo.json ./

# Copiar package.json de todos os workspaces para cache de layers
COPY config/package.json ./config/
COPY apps/web/package.json ./apps/web/
COPY packages/*/package.json ./packages/*/
COPY tooling/*/package.json ./tooling/*/

# Instalar dependências
RUN pnpm install --frozen-lockfile

# Stage 2: Build da aplicação
FROM base AS builder

# Copiar dependências instaladas
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/web/node_modules ./apps/web/node_modules

# Copiar configuração do monorepo
COPY pnpm-lock.yaml ./
COPY pnpm-workspace.yaml ./
COPY package.json ./
COPY turbo.json ./

# Copiar código fonte
COPY config ./config
COPY packages ./packages
COPY tooling ./tooling
COPY apps/web ./apps/web

# Build da aplicação
RUN pnpm turbo run build --filter=@repo/web

# Stage 3: Runtime de produção
FROM node:22-alpine AS runner

# Instalar dependências do sistema
RUN apk add --no-cache libc6-compat

# Criar usuário não-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

WORKDIR /app

# Configurar ambiente de produção
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Copiar aplicação buildada
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

# Mudar para usuário não-root
USER nextjs

# Expor porta
EXPOSE 3000

# Health check para Google Cloud Run
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Comando de inicialização
CMD ["node", "apps/web/server.js"]
