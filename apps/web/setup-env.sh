#!/bin/bash

# 🚀 Script de Configuração do Ambiente - SuperGateway
# Este script configura as variáveis de ambiente necessárias para o build

set -e

echo "🚀 Configurando ambiente do SuperGateway..."

# Verificar se estamos na pasta correta
if [ ! -f "package.json" ] || [ ! -f "next.config.ts" ]; then
    echo "❌ Erro: Execute este script na pasta apps/web"
    exit 1
fi

# Verificar se .env.local já existe
if [ -f ".env.local" ]; then
    echo "⚠️  Arquivo .env.local já existe. Fazendo backup..."
    cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
fi

# Gerar chave secreta aleatória
SECRET_KEY=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)

# Criar .env.local
cat > .env.local << EOF
# =============================================================================
# CONFIGURAÇÃO AUTOMÁTICA DO SUPERGATEWAY
# Gerado em: $(date)
# =============================================================================

# 🔐 AUTENTICAÇÃO (OBRIGATÓRIO)
BETTER_AUTH_SECRET=${SECRET_KEY}

# 🗄️  BANCO DE DADOS (OBRIGATÓRIO)
# Altere para sua configuração real
DATABASE_URL=postgresql://postgres:password@localhost:5432/supergateway

# 📧 EMAIL (OBRIGATÓRIO)
# Obtenha sua chave em: https://resend.com/api-keys
RESEND_API_KEY=re_1234567890abcdef

# 🎯 CHATWOOT (OPCIONAL - para evitar erros de build)
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_ACCOUNT_ID=1
CHATWOOT_API_ACCESS_TOKEN=dummy-token
CHATWOOT_WHATSAPP_INBOX_ID=1
CHATWOOT_SMS_INBOX_ID=2
CHATWOOT_EMAIL_INBOX_ID=3
CHATWOOT_WEBHOOKS_ENABLED=false
CHATWOOT_WEBHOOK_SECRET=dummy-secret

# 🔗 PROVEDORES SOCIAIS (OPCIONAL)
GOOGLE_CLIENT_ID=dummy-google-client-id
GOOGLE_CLIENT_SECRET=dummy-google-client-secret
GITHUB_CLIENT_ID=dummy-github-client-id
GITHUB_CLIENT_SECRET=dummy-github-client-secret

# 💳 STRIPE (OPCIONAL)
STRIPE_SECRET_KEY=sk_test_dummy
STRIPE_WEBHOOK_SECRET=whsec_dummy
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_dummy
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY=price_dummy_monthly
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY=price_dummy_yearly
NEXT_PUBLIC_PRICE_ID_LIFETIME=price_dummy_lifetime

# ☁️  AWS S3 (OPCIONAL)
AWS_ACCESS_KEY_ID=dummy-aws-key
AWS_SECRET_ACCESS_KEY=dummy-aws-secret
AWS_REGION=us-east-1
NEXT_PUBLIC_AVATARS_BUCKET_NAME=supergateway-avatars

# ⚙️  NEXT.JS
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=dummy-nextauth-secret
NODE_ENV=development
EOF

echo "✅ Arquivo .env.local criado com sucesso!"
echo ""
echo "🔧 PRÓXIMOS PASSOS:"
echo "1. Edite .env.local e configure as variáveis reais:"
echo "   - DATABASE_URL (para seu PostgreSQL)"
echo "   - RESEND_API_KEY (obtenha em https://resend.com)"
echo ""
echo "2. Teste o build:"
echo "   pnpm run build"
echo ""
echo "3. Se tudo funcionar, você pode personalizar as outras variáveis"
echo ""
echo "📚 Consulte SETUP_ENV.md para mais detalhes"
echo ""
echo "🚀 Ambiente configurado! Boa sorte!"
