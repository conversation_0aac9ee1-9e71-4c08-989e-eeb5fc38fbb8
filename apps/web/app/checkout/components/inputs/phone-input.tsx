'use client';

import { Input } from '@ui/components/input';
import { forwardRef } from 'react';

interface PhoneInputProps {
	value: string;
	onChange: (value: string) => void;
	error?: string;
	placeholder?: string;
}

function formatPhone(value: string): string {
	const numbers = value.replace(/\D/g, '');
	if (numbers.length <= 10) {
		return numbers
			.replace(/(\d{2})(\d)/, '($1) $2')
			.replace(/(\d{4})(\d)/, '$1-$2')
			.replace(/(-\d{4})\d+?$/, '$1');
	} else {
		return numbers
			.replace(/(\d{2})(\d)/, '($1) $2')
			.replace(/(\d{5})(\d)/, '$1-$2')
			.replace(/(-\d{4})\d+?$/, '$1');
	}
}

export const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
	({ value, onChange, error, placeholder = '(11) 99999-9999', ...props }, ref) => {
		const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const formatted = formatPhone(e.target.value);
			onChange(formatted);
		};

		return (
			<div className='space-y-1'>
				<Input
					{...props}
					ref={ref}
					value={value}
					onChange={handleChange}
					placeholder={placeholder}
					maxLength={15}
					className={error ? 'border-red-500' : ''}
				/>
				{error && <p className='text-sm text-red-500'>{error}</p>}
			</div>
		);
	}
);

PhoneInput.displayName = 'PhoneInput';