'use client';

import { Input } from '@ui/components/input';
import { forwardRef } from 'react';

interface CpfInputProps {
	value: string;
	onChange: (value: string) => void;
	error?: string;
	placeholder?: string;
}

function formatCpf(value: string): string {
	const numbers = value.replace(/\D/g, '');
	return numbers
		.replace(/(\d{3})(\d)/, '$1.$2')
		.replace(/(\d{3})(\d)/, '$1.$2')
		.replace(/(\d{3})(\d{1,2})/, '$1-$2')
		.replace(/(-\d{2})\d+?$/, '$1');
}

function validateCpf(cpf: string): boolean {
	const numbers = cpf.replace(/\D/g, '');
	if (numbers.length !== 11) return false;
	if (/^(\d)\1{10}$/.test(numbers)) return false;

	let sum = 0;
	for (let i = 0; i < 9; i++) {
		sum += parseInt(numbers.charAt(i)) * (10 - i);
	}
	let remainder = (sum * 10) % 11;
	if (remainder === 10 || remainder === 11) remainder = 0;
	if (remainder !== parseInt(numbers.charAt(9))) return false;

	sum = 0;
	for (let i = 0; i < 10; i++) {
		sum += parseInt(numbers.charAt(i)) * (11 - i);
	}
	remainder = (sum * 10) % 11;
	if (remainder === 10 || remainder === 11) remainder = 0;
	if (remainder !== parseInt(numbers.charAt(10))) return false;

	return true;
}

export const CpfInput = forwardRef<HTMLInputElement, CpfInputProps>(
	({ value, onChange, error, placeholder = '000.000.000-00', ...props }, ref) => {
		const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const formatted = formatCpf(e.target.value);
			onChange(formatted);
		};

		return (
			<div className='space-y-1'>
				<Input
					{...props}
					ref={ref}
					value={value}
					onChange={handleChange}
					placeholder={placeholder}
					maxLength={14}
					className={error ? 'border-red-500' : ''}
				/>
				{error && <p className='text-sm text-red-500'>{error}</p>}
			</div>
		);
	}
);

CpfInput.displayName = 'CpfInput';