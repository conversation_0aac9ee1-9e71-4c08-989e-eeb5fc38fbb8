'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { CheckoutFormData } from './types';
import { CpfInput } from './inputs/cpf-input';
import { PhoneInput } from './inputs/phone-input';

export function CustomerForm() {
	const {
		register,
		formState: { errors },
		watch,
		setValue,
	} = useFormContext<CheckoutFormData>();

	return (
		<Card>
			<CardHeader>
				<CardTitle>Dados pessoais</CardTitle>
			</CardHeader>
			<CardContent className='space-y-4'>
				<div className='grid gap-4 md:grid-cols-2'>
					<div className='space-y-2'>
						<Label htmlFor='name'>Nome completo</Label>
						<Input
							id='name'
							placeholder='Seu nome completo'
							{...register('customerData.name')}
							className={errors.customerData?.name ? 'border-red-500' : ''}
						/>
						{errors.customerData?.name && (
							<p className='text-sm text-red-500'>
								{errors.customerData.name.message}
							</p>
						)}
					</div>

					<div className='space-y-2'>
						<Label htmlFor='email'>E-mail</Label>
						<Input
							id='email'
							type='email'
							placeholder='<EMAIL>'
							{...register('customerData.email')}
							className={errors.customerData?.email ? 'border-red-500' : ''}
						/>
						{errors.customerData?.email && (
							<p className='text-sm text-red-500'>
								{errors.customerData.email.message}
							</p>
						)}
					</div>
				</div>

				<div className='grid gap-4 md:grid-cols-2'>
					<div className='space-y-2'>
						<Label htmlFor='cpf'>CPF</Label>
						<CpfInput
							value={watch('customerData.cpf')}
							onChange={(value) => setValue('customerData.cpf', value)}
							error={errors.customerData?.cpf?.message}
						/>
					</div>

					<div className='space-y-2'>
						<Label htmlFor='phone'>Telefone</Label>
						<PhoneInput
							value={watch('customerData.phone')}
							onChange={(value) => setValue('customerData.phone', value)}
							error={errors.customerData?.phone?.message}
						/>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}