"use client";

import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import type { CheckoutFormData } from "./types";

export function CouponForm() {
  const { setValue, watch } = useFormContext<CheckoutFormData>();
  const [couponInput, setCouponInput] = useState("");
  const [isApplying, setIsApplying] = useState(false);
  const [couponMessage, setCouponMessage] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);
  
  const appliedCoupon = watch("couponCode");

  const applyCoupon = async () => {
    if (!couponInput.trim()) return;
    
    setIsApplying(true);
    setCouponMessage(null);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (couponInput.toLowerCase() === "desconto10") {
        setValue("couponCode", couponInput);
        setCouponMessage({
          type: "success",
          message: "Cupom aplicado com sucesso! 10% de desconto"
        });
        setCouponInput("");
      } else {
        setCouponMessage({
          type: "error",
          message: "Cupom inválido ou expirado"
        });
      }
    } catch (error) {
      setCouponMessage({
        type: "error",
        message: "Erro ao aplicar cupom. Tente novamente."
      });
    } finally {
      setIsApplying(false);
    }
  };

  const removeCoupon = () => {
    setValue("couponCode", "");
    setCouponMessage(null);
  };

  return (
    <div className="space-y-3">
      <Label>Cupom de desconto</Label>
      
      {appliedCoupon ? (
        <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
          <div>
            <span className="font-medium text-green-800">
              Cupom: {appliedCoupon}
            </span>
            <p className="text-sm text-green-600">
              10% de desconto aplicado
            </p>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={removeCoupon}
            className="text-green-700 hover:text-green-900"
          >
            Remover
          </Button>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              placeholder="Digite seu cupom"
              value={couponInput}
              onChange={(e) => setCouponInput(e.target.value.toUpperCase())}
              onKeyPress={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  applyCoupon();
                }
              }}
            />
            <Button
              type="button"
              onClick={applyCoupon}
              disabled={!couponInput.trim() || isApplying}
              variant="outline"
            >
              {isApplying ? "Aplicando..." : "Aplicar"}
            </Button>
          </div>
          
          {couponMessage && (
            <p className={`text-sm ${
              couponMessage.type === "success" 
                ? "text-green-600" 
                : "text-red-600"
            }`}>
              {couponMessage.message}
            </p>
          )}
        </div>
      )}
    </div>
  );
}