import "server-only";
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { CheckoutHeader } from '../components/checkout-header';

interface CheckoutPageProps {
	params: Promise<{
		productId: string;
	}>;
}

export default async function CheckoutPage({ params }: CheckoutPageProps) {
	const { productId } = await params;

	if (!productId) {
		redirect('/');
	}

	try {
		// Mock product data - diferentes cenários para testes
		const mockProducts: Record<string, any> = {
			'curso-completo': {
				id: 'curso-completo',
				title: 'Curso Completo de Marketing Digital',
				description: 'Aprenda marketing digital do zero ao avançado',
				type: 'COURSE' as const,
				price: 497.00,
				regularPrice: 997.00,
				installmentsLimit: 12,
				enableInstallments: true,
				acceptedPayments: ['CREDIT_CARD', 'PIX', 'BOLETO'],
				offers: [
					{
						id: 'bonus-templates',
						title: 'Pack de Templates',
						description: '50+ templates prontos para usar',
						price: 97.00,
						type: 'ORDER_BUMP'
					},
					{
						id: 'mentoria-grupo',
						title: 'Mentoria em Grupo',
						description: 'Acesso a grupo VIP no Telegram',
						price: 197.00,
						type: 'ORDER_BUMP'
					}
				]
			},
			'ebook-vendas': {
				id: 'ebook-vendas',
				title: 'E-book: Técnicas de Vendas',
				description: 'Guia completo com estratégias de vendas',
				type: 'EBOOK' as const,
				price: 47.00,
				regularPrice: 97.00,
				installmentsLimit: 3,
				enableInstallments: false,
				acceptedPayments: ['CREDIT_CARD', 'PIX'],
				offers: [
					{
						id: 'checklist-vendas',
						title: 'Checklist de Vendas',
						description: 'Lista completa para não esquecer nada',
						price: 27.00,
						type: 'ORDER_BUMP'
					}
				]
			},
			'mentoria-premium': {
				id: 'mentoria-premium',
				title: 'Mentoria Premium 1:1',
				description: 'Mentoria individual personalizada',
				type: 'MENTORING' as const,
				price: 1497.00,
				regularPrice: 2497.00,
				installmentsLimit: 12,
				enableInstallments: true,
				acceptedPayments: ['CREDIT_CARD', 'PIX'],
				offers: [
					{
						id: 'sessao-extra',
						title: 'Sessão Extra',
						description: 'Uma sessão adicional de 1 hora',
						price: 297.00,
						type: 'ORDER_BUMP'
					},
					{
						id: 'material-exclusivo',
						title: 'Material Exclusivo',
						description: 'Acesso a materiais premium',
						price: 197.00,
						type: 'ORDER_BUMP'
					}
				]
			}
		};

		// Produto padrão caso não encontre o ID específico
		const defaultProduct = {
			id: productId,
			title: 'Produto de Exemplo',
			description: 'Descrição do produto de exemplo para testes',
			type: 'COURSE' as const,
			price: 197.00,
			regularPrice: 297.00,
			installmentsLimit: 12,
			enableInstallments: true,
			acceptedPayments: ['CREDIT_CARD', 'PIX', 'BOLETO'],
			offers: [
				{
					id: 'offer-1',
					title: 'Bônus Exclusivo',
					description: 'Material complementar exclusivo',
					price: 47.00,
					type: 'ORDER_BUMP'
				}
			]
		};

		const product = mockProducts[productId] || defaultProduct;

		// Adicionar propriedades padrão
		const productWithDefaults = {
			...product,
			thumbnail: '/images/product-placeholder.jpg',
			checkoutBanner: null,
			checkoutType: 'DEFAULT' as const,
			checkoutSettings: {},
			customCheckoutUrl: null,
			successUrl: null,
			cancelUrl: null,
			termsUrl: null
		};

		// Mock creator data
		const creator = {
			id: "creator-1",
			name: "Instrutor Exemplo"
		};

		return (
			<div className='min-h-screen bg-gray-50/50'>
				<CheckoutHeader />

				<div className='mx-3 md:mx-auto md:container py-5'>
					{/* Banner do checkout se disponível */}
					{productWithDefaults.checkoutBanner && (
						<div className='mb-6'>
							<img
								src={productWithDefaults.checkoutBanner}
								alt="Banner do checkout"
								className='w-full h-auto rounded-lg shadow-sm'
							/>
						</div>
					)}

					<CheckoutForm product={productWithDefaults} />
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading checkout page:', error);
		redirect('/');
	}
}