'use client';

import { useSearchParams } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Copy, QrCode, Clock, CheckCircle } from 'lucide-react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

export default function CheckoutPixPage() {
	const searchParams = useSearchParams();
	const orderId = searchParams.get('orderId');
	const [timeLeft, setTimeLeft] = useState(15 * 60);
	const [pixCode] = useState('00020126580014br.gov.bcb.pix013636c4b8e5-4d4e-4c4e-8b4e-4d4e4c4e8b4e5204000053039865802BR5925SupGateway LTDA6009SAO PAULO62070503***6304A1B2');

	useEffect(() => {
		const timer = setInterval(() => {
			setTimeLeft((prev) => {
				if (prev <= 1) {
					clearInterval(timer);
					return 0;
				}
				return prev - 1;
			});
		}, 1000);

		return () => clearInterval(timer);
	}, []);

	const formatTime = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
	};

	const copyPixCode = () => {
		navigator.clipboard.writeText(pixCode);
		toast.success('Código PIX copiado!');
	};

	return (
		<div className='min-h-screen bg-gray-50 py-12'>
			<div className='mx-auto max-w-2xl px-4'>
				<Card>
					<CardHeader className='text-center'>
						<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100'>
							<QrCode className='h-8 w-8 text-blue-600' />
						</div>
						<CardTitle className='text-2xl text-blue-600'>
							Pagamento via PIX
						</CardTitle>
						<p className='text-gray-600 mt-2'>
							Escaneie o QR Code ou copie o código PIX
						</p>
					</CardHeader>

					<CardContent className='space-y-6'>
						<div className='rounded-lg bg-gray-50 p-4 text-center'>
							<p className='text-sm text-gray-600'>Número do pedido</p>
							<p className='font-mono text-lg font-semibold'>{orderId}</p>
						</div>

						{timeLeft > 0 ? (
							<div className='flex items-center justify-center gap-2 rounded-lg bg-orange-50 p-4 text-orange-800'>
								<Clock className='h-5 w-5' />
								<span className='font-semibold'>
									Tempo restante: {formatTime(timeLeft)}
								</span>
							</div>
						) : (
							<div className='rounded-lg bg-red-50 p-4 text-center text-red-800'>
								<p className='font-semibold'>Código PIX expirado</p>
								<p className='text-sm'>Gere um novo código para continuar</p>
							</div>
						)}

						<div className='space-y-4'>
							<div className='mx-auto h-64 w-64 rounded-lg bg-white p-4 shadow-sm'>
								<div className='flex h-full items-center justify-center text-gray-400'>
									<QrCode className='h-32 w-32' />
								</div>
							</div>

							<div className='space-y-2'>
								<p className='text-sm font-semibold text-gray-700'>
									Código PIX Copia e Cola:
								</p>
								<div className='flex gap-2'>
									<div className='flex-1 rounded-lg border bg-gray-50 p-3'>
										<p className='break-all font-mono text-xs text-gray-600'>
											{pixCode}
										</p>
									</div>
									<Button
										variant='outline'
										size='sm'
										onClick={copyPixCode}
										className='shrink-0'
									>
										<Copy className='h-4 w-4' />
									</Button>
								</div>
							</div>
						</div>

						<div className='space-y-4'>
							<h3 className='font-semibold'>Como pagar:</h3>
							<ol className='space-y-2 text-left text-sm text-gray-600'>
								<li>1. Abra o app do seu banco</li>
								<li>2. Escolha a opção PIX</li>
								<li>3. Escaneie o QR Code ou cole o código</li>
								<li>4. Confirme o pagamento</li>
							</ol>
						</div>

						<div className='rounded-lg border-l-4 border-green-500 bg-green-50 p-4 text-left'>
							<div className='flex items-center gap-2 text-green-800'>
								<CheckCircle className='h-5 w-5' />
								<p className='text-sm'>
									<strong>Pagamento instantâneo!</strong> Após a confirmação,
									você será redirecionado automaticamente.
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
