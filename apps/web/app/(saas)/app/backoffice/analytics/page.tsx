import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  UsersIcon, 
  BuildingIcon, 
  CreditCardIcon, 
  ActivityIcon,
  BarChart3Icon,
  PieChartIcon,
  CalendarIcon,
  DownloadIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";

export default async function AdminAnalyticsPage() {
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  if (session.user.role !== "admin") {
    redirect("/app");
  }

  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const [currentStats, previousStats] = await Promise.all([
    Promise.all([
      db.organization.count({
        where: {
          createdAt: {
            gte: currentMonth,
          },
        },
      }),
      db.user.count({
        where: {
          createdAt: {
            gte: currentMonth,
          },
        },
      }),
      db.transaction.aggregate({
        where: {
          createdAt: {
            gte: currentMonth,
          },
          status: "COMPLETED",
        },
        _sum: { amountCents: true },
        _count: true,
      }),
    ]),
    Promise.all([
      db.organization.count({
        where: {
          createdAt: {
            gte: lastMonth,
            lt: currentMonth,
          },
        },
      }),
      db.user.count({
        where: {
          createdAt: {
            gte: lastMonth,
            lt: currentMonth,
          },
        },
      }),
      db.transaction.aggregate({
        where: {
          createdAt: {
            gte: lastMonth,
            lt: currentMonth,
          },
          status: "COMPLETED",
        },
        _sum: { amountCents: true },
        _count: true,
      }),
    ]),
  ]);

  const [currentOrgs, currentUsers, currentTransactions] = currentStats;
  const [previousOrgs, previousUsers, previousTransactions] = previousStats;

  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  const metrics = [
    {
      title: "Novas Organizações",
      value: currentOrgs,
      previous: previousOrgs,
      icon: BuildingIcon,
      color: "blue",
    },
    {
      title: "Novos Usuários",
      value: currentUsers,
      previous: previousUsers,
      icon: UsersIcon,
      color: "green",
    },
    {
      title: "Transações",
      value: currentTransactions._count,
      previous: previousTransactions._count,
      icon: CreditCardIcon,
      color: "purple",
    },
    {
      title: "Receita",
      value: (currentTransactions._sum?.amountCents || 0) / 100,
      previous: (previousTransactions._sum?.amountCents || 0) / 100,
      icon: TrendingUpIcon,
      color: "orange",
      isCurrency: true,
    },
  ];

  const topOrganizations = await db.organization.findMany({
    take: 5,
    include: {
      _count: {
        select: {
          members: true,
        },
      },
    },
    orderBy: {
      members: {
        _count: "desc",
      },
    },
  });

  const recentActivity = await db.user.findMany({
    take: 10,
    orderBy: {
      createdAt: "desc",
    },
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics</h1>
          <p className="text-muted-foreground">
            Análise detalhada do desempenho da plataforma
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Período
          </Button>
          <Button variant="outline" size="sm">
            <DownloadIcon className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => {
          const growth = calculateGrowth(metric.value, metric.previous);
          const isPositive = growth >= 0;
          const IconComponent = metric.icon;

          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <IconComponent className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metric.isCurrency
                    ? `R$ ${metric.value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                    : metric.value.toLocaleString()}
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    vs. mês anterior
                  </p>
                  <div className={`flex items-center text-xs ${
                    isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {isPositive ? (
                      <TrendingUpIcon className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDownIcon className="h-3 w-3 mr-1" />
                    )}
                    {Math.abs(growth).toFixed(1)}%
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3Icon className="h-5 w-5" />
              Top Organizações
            </CardTitle>
            <CardDescription>
              Organizações com mais membros
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topOrganizations.map((org, index) => (
                <div key={org.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium">{org.name}</p>
                      <p className="text-sm text-muted-foreground">{org.slug}</p>
                    </div>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800 border border-blue-200">
                    {org._count.members} membros
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ActivityIcon className="h-5 w-5" />
              Atividade Recente
            </CardTitle>
            <CardDescription>
              Últimos usuários cadastrados
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((user) => (
                <div key={user.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <UsersIcon className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">
                      {new Date(user.createdAt).toLocaleDateString('pt-BR')}
                    </p>
                    <Badge className="text-xs bg-gray-100 text-gray-800 border border-gray-200">
                      {user.role}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Distribuição por Plano
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Gratuito</span>
                <div className="flex items-center gap-2">
                  <div className="w-12 h-2 bg-gray-200 rounded-full">
                    <div className="w-8 h-2 bg-gray-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium">67%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Pro</span>
                <div className="flex items-center gap-2">
                  <div className="w-12 h-2 bg-gray-200 rounded-full">
                    <div className="w-4 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium">25%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Enterprise</span>
                <div className="flex items-center gap-2">
                  <div className="w-12 h-2 bg-gray-200 rounded-full">
                    <div className="w-1 h-2 bg-purple-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium">8%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Crescimento Mensal</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Janeiro</span>
                <span className="font-medium">+12%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Fevereiro</span>
                <span className="font-medium">+8%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Março</span>
                <span className="font-medium">+15%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Abril</span>
                <span className="font-medium">+23%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Status do Sistema</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">API</span>
                <Badge className="bg-green-100 text-green-800 border border-green-200">
                  Online
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Database</span>
                <Badge className="bg-green-100 text-green-800 border border-green-200">
                  Online
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Storage</span>
                <Badge className="bg-green-100 text-green-800 border border-green-200">
                  Online
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Email</span>
                <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200">
                  Degraded
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}