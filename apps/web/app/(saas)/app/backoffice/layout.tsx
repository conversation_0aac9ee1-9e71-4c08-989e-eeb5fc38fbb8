import { getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { AdminSidebar } from "@saas/admin/components/AdminSidebar";
import { AppWrapper } from "@saas/shared/components/AppWrapper";

export default async function AdminLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	// Verifica se o usuário é admin
	if (session.user.role !== "admin") {
		redirect("/app");
	}

	return (
		<AppWrapper>
			<div className="flex gap-6">
				<AdminSidebar />
				<main className="flex-1">
					{children}
				</main>
			</div>
		</AppWrapper>
	);
}
