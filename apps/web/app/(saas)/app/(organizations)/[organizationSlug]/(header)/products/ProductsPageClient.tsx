"use client";

import { useState } from "react";
import { ProductsGrid } from "@saas/products/components/ProductsGrid";
import { ProductFilters } from "@saas/products/components/ProductFilters";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { ProductFilters as ProductFiltersType } from "@saas/products/components/ProductFiltersSheet";

interface ProductsPageClientProps {
  organization: any; // Substitua por um tipo mais específico se disponível
}

export function ProductsPageClient({ organization }: ProductsPageClientProps) {
  const [filters, setFilters] = useState<ProductFiltersType>({
    searchTerm: "",
    status: [],
    category: [],
    type: [],
    priceRange: { min: null, max: null },
    hasCommission: null,
  });

  const handleFiltersChange = (newFilters: ProductFiltersType) => {
    setFilters(newFilters);
    // Aqui você pode implementar a lógica de filtragem real
    console.log("Filtros aplicados:", newFilters);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Produtos"
        subtitle="Gerencie seus produtos e serviços"
        actions={
          <Link href={`/app/${organization.slug}/products/new`}>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Novo Produto
            </Button>
          </Link>
        }
      />

      <ProductFilters
        organizationId={organization.id}
        onFiltersChange={handleFiltersChange}
      />
      <ProductsGrid
        organizationId={organization.id}
        filters={filters}
      />
    </div>
  );
}
