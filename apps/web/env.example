# =============================================================================
# CONFIGURAÇÕES DE AMBIENTE PARA SUPERGATEWAY
# =============================================================================
# Copie este arquivo para .env.local e preencha os valores necessários

# =============================================================================
# AUTENTICAÇÃO (BETTER AUTH)
# =============================================================================
# Chave secreta para criptografia de sessões (OBRIGATÓRIO)
BETTER_AUTH_SECRET=your-super-secret-key-here-min-32-chars

# =============================================================================
# PROVEDORES SOCIAIS (OPCIONAL - para login com Google/GitHub)
# =============================================================================
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# =============================================================================
# BANCO DE DADOS (OBRIGATÓRIO)
# =============================================================================
# PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/supergateway

# =============================================================================
# EMAIL (OBRIGATÓRIO para funcionalidades de email)
# =============================================================================
# Resend (recomendado)
RESEND_API_KEY=your-resend-api-key

# Ou SMTP tradicional
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# =============================================================================
# PAGAMENTOS (OPCIONAL - para funcionalidades de billing)
# =============================================================================
# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# IDs dos produtos Stripe
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY=price_...
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY=price_...
NEXT_PUBLIC_PRICE_ID_LIFETIME=price_...

# =============================================================================
# STORAGE (OPCIONAL - para upload de arquivos)
# =============================================================================
# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
NEXT_PUBLIC_AVATARS_BUCKET_NAME=supergateway-avatars

# =============================================================================
# CHATWOOT (OPCIONAL - para suporte ao cliente)
# =============================================================================
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_ACCOUNT_ID=your-account-id
CHATWOOT_API_ACCESS_TOKEN=your-api-token
CHATWOOT_WHATSAPP_INBOX_ID=1
CHATWOOT_SMS_INBOX_ID=2
CHATWOOT_EMAIL_INBOX_ID=3
CHATWOOT_WEBHOOKS_ENABLED=false
CHATWOOT_WEBHOOK_SECRET=your-webhook-secret

# =============================================================================
# CONFIGURAÇÕES DO NEXT.JS
# =============================================================================
# URL base da aplicação
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret

# =============================================================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# =============================================================================
# Ambiente
NODE_ENV=development

# =============================================================================
# CONFIGURAÇÕES DE LOG
# =============================================================================
# Log level (debug, info, warn, error)
LOG_LEVEL=info

# =============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# =============================================================================
# Chave para criptografia de cookies
COOKIE_SECRET=your-cookie-secret-key

# =============================================================================
# CONFIGURAÇÕES DE CACHE
# =============================================================================
# Redis (OPCIONAL - para cache distribuído)
REDIS_URL=redis://localhost:6379

# =============================================================================
# CONFIGURAÇÕES DE MONITORAMENTO
# =============================================================================
# Sentry (OPCIONAL - para monitoramento de erros)
SENTRY_DSN=your-sentry-dsn

# =============================================================================
# CONFIGURAÇÕES DE ANALYTICS
# =============================================================================
# Google Analytics (OPCIONAL)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# =============================================================================
# CONFIGURAÇÕES DE NOTIFICAÇÕES
# =============================================================================
# Push notifications (OPCIONAL)
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
