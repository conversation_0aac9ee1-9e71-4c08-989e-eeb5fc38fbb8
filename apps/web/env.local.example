# =============================================================================
# VARIÁVEIS MÍNIMAS PARA BUILD FUNCIONAR
# =============================================================================

# OBRIGATÓRIO: Chave secreta para autenticação (mínimo 32 caracteres)
BETTER_AUTH_SECRET=supergateway-secret-key-2024-minimum-32-chars-long

# OBRIGATÓRIO: URL do banco de dados PostgreSQL
DATABASE_URL=postgresql://postgres:password@localhost:5432/supergateway

# OBRIGATÓRIO: Chave da API Resend para emails
RESEND_API_KEY=re_1234567890abcdef

# OPCIONAL: Configuração do Chatwoot (para evitar erro durante build)
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_ACCOUNT_ID=1
CHATWOOT_API_ACCESS_TOKEN=dummy-token
CHATWOOT_WHATSAPP_INBOX_ID=1
CHATWOOT_SMS_INBOX_ID=2
CHATWOOT_EMAIL_INBOX_ID=3
CHATWOOT_WEBHOOKS_ENABLED=false
CHATWOOT_WEBHOOK_SECRET=dummy-secret

# OPCIONAL: Provedores sociais (para evitar warnings)
GOOGLE_CLIENT_ID=dummy-google-client-id
GOOGLE_CLIENT_SECRET=dummy-google-client-secret
GITHUB_CLIENT_ID=dummy-github-client-id
GITHUB_CLIENT_SECRET=dummy-github-client-secret

# OPCIONAL: Stripe (para funcionalidades de pagamento)
STRIPE_SECRET_KEY=sk_test_dummy
STRIPE_WEBHOOK_SECRET=whsec_dummy
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_dummy
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY=price_dummy_monthly
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY=price_dummy_yearly
NEXT_PUBLIC_PRICE_ID_LIFETIME=price_dummy_lifetime

# OPCIONAL: AWS S3 (para upload de arquivos)
AWS_ACCESS_KEY_ID=dummy-aws-key
AWS_SECRET_ACCESS_KEY=dummy-aws-secret
AWS_REGION=us-east-1
NEXT_PUBLIC_AVATARS_BUCKET_NAME=supergateway-avatars

# OPCIONAL: Configurações do Next.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=dummy-nextauth-secret
NODE_ENV=development
