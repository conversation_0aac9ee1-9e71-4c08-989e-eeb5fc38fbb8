"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  PackageIcon,
  EditIcon,
  MoreHorizontalIcon,
  EyeIcon,
  PlusIcon
} from "lucide-react";
import { ProductFilters } from "./ProductFiltersSheet";

interface ProductsGridProps {
  organizationId: string;
  filters?: ProductFilters;
}

interface Product {
  id: string;
  name: string;
  type: string;
  status: "active" | "inactive" | "draft";
  category: "authorial" | "affiliation" | "coproduction";
  image: string;
  description: string;
  price: number;
  commission?: number;
}

// Dados mockados baseados na imagem
const mockProducts: Product[] = [
  {
    id: "1",
    name: "Plataforma Digital",
    type: "SERVIÇO",
    status: "active",
    category: "authorial",
    image: "/images/products/platform-digital.jpg",
    description: "Plataforma completa para gestão de negócios digitais",
    price: 299.90
  },
  {
    id: "2",
    name: "CineFlick Card",
    type: "SERVIÇO",
    status: "active",
    category: "affiliation",
    image: "/images/products/cineflick-card.jpg",
    description: "Cartão de desconto para cinemas com 60% de comissão",
    price: 99.90,
    commission: 60
  }
];

export function ProductsGrid({ organizationId, filters }: ProductsGridProps) {
  const getStatusBadge = (status: Product["status"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 border border-green-200 text-xs font-medium">ATIVO</Badge>;
      case "inactive":
        return <Badge className="bg-red-100 text-red-800 border border-red-200 text-xs font-medium">INATIVO</Badge>;
      case "draft":
        return <Badge className="bg-yellow-100 text-yellow-800 border border-yellow-200 text-xs font-medium">RASCUNHO</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border border-gray-200 text-xs font-medium">DESCONHECIDO</Badge>;
    }
  };

  const getCategoryBadge = (category: Product["category"]) => {
    const colors = {
      "authorial": "bg-blue-100 text-blue-800 border border-blue-200",
      "affiliation": "bg-purple-100 text-purple-800 border border-purple-200",
      "coproduction": "bg-orange-100 text-orange-800 border border-orange-200"
    };

    const labels = {
      "authorial": "AUTORAL",
      "affiliation": "AFILIAÇÃO",
      "coproduction": "COPRODUÇÃO"
    };

    return (
      <Badge className={`${colors[category]} text-xs font-medium`}>
        {labels[category]}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    return (
      <Badge className="bg-blue-100 text-blue-800 border border-blue-200 text-xs font-medium">
        {type}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Produtos</CardTitle>
        <CardDescription>
          Gerencie seus produtos e serviços
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {mockProducts.map((product) => (
            <Card key={product.id} className="hover:shadow-lg transition-all duration-200 hover:scale-[1.02] group">
              <CardHeader className="pb-3">
                {/* Imagem do Produto */}
                <div className="aspect-square bg-muted rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover rounded-lg group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
                      <PackageIcon className="h-16 w-16 text-blue-400" />
                    </div>
                  )}
                </div>

                {/* Header do Card */}
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg font-semibold leading-tight line-clamp-2 text-gray-900">
                      {product.name}
                    </CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    >
                      <MoreHorizontalIcon className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Tags */}
                  <div className="flex items-center gap-2 flex-wrap">
                    {getTypeBadge(product.type)}
                    {getStatusBadge(product.status)}
                    {getCategoryBadge(product.category)}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Descrição */}
                  <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                    {product.description}
                  </p>

                  {/* Preço e Comissão */}
                  <div className="flex items-center justify-between">
                    <div className="text-xl font-bold text-gray-900">
                      R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </div>

                    {product.commission && (
                      <Badge className="bg-green-50 text-green-700 border border-green-200 text-xs font-medium">
                        {product.commission}% COMISSÃO
                      </Badge>
                    )}
                  </div>

                  {/* Botões de Ação */}
                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1 hover:bg-gray-50">
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Visualizar
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 hover:bg-gray-50">
                      <EditIcon className="h-4 w-4 mr-2" />
                      Editar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Card para adicionar novo produto */}
          <Card className="hover:shadow-lg transition-all duration-200 hover:scale-[1.02] border-dashed border-2 border-gray-300 hover:border-gray-400 group">
            <CardContent className="flex flex-col items-center justify-center h-64 p-6">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-gray-200 transition-colors duration-200">
                <PlusIcon className="h-8 w-8 text-gray-500" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center">
                Adicionar Produto
              </h3>
              <p className="text-sm text-gray-600 text-center mb-6 leading-relaxed">
                Crie um novo produto ou serviço para sua organização
              </p>
              <Button className="group-hover:bg-blue-600 transition-colors duration-200">
                <PackageIcon className="h-4 w-4 mr-2" />
                Novo Produto
              </Button>
            </CardContent>
          </Card>
        </div>

        {mockProducts.length === 0 && (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <PackageIcon className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum produto encontrado</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Comece criando seu primeiro produto para começar a vender
            </p>
            <Button>
              <PackageIcon className="h-4 w-4 mr-2" />
              Criar Produto
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
