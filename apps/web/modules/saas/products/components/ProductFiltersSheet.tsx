"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Label } from "@ui/components/label";
import { Checkbox } from "@ui/components/checkbox";
import { Separator } from "@ui/components/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@ui/components/sheet";
import {
  FilterIcon,
  SearchIcon,
  XIcon,
  PackageIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  DollarSignIcon,
  TagIcon,
} from "lucide-react";

interface ProductFiltersSheetProps {
  onFiltersChange: (filters: ProductFilters) => void;
  activeFilters: ProductFilters;
}

export interface ProductFilters {
  searchTerm: string;
  status: string[];
  category: string[];
  type: string[];
  priceRange: { min: number | null; max: number | null };
  hasCommission: boolean | null;
}

const statusOptions = [
  { id: "active", name: "Ativos", icon: CheckCircleIcon, color: "text-green-600" },
  { id: "inactive", name: "Inativos", icon: XCircleIcon, color: "text-red-600" },
  { id: "draft", name: "Rascunho", icon: ClockIcon, color: "text-yellow-600" },
];

const categoryOptions = [
  { id: "authorial", name: "Autorais", count: 8 },
  { id: "affiliation", name: "Afiliação", count: 3 },
  { id: "coproduction", name: "Coprodução", count: 1 },
];

const typeOptions = [
  { id: "service", name: "Serviço", count: 10 },
  { id: "product", name: "Produto", count: 2 },
];

export function ProductFiltersSheet({ onFiltersChange, activeFilters }: ProductFiltersSheetProps) {
  const [filters, setFilters] = useState<ProductFilters>(activeFilters);
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleStatusToggle = (statusId: string) => {
    const newStatus = filters.status.includes(statusId)
      ? filters.status.filter(id => id !== statusId)
      : [...filters.status, statusId];
    handleFilterChange('status', newStatus);
  };

  const handleCategoryToggle = (categoryId: string) => {
    const newCategory = filters.category.includes(categoryId)
      ? filters.category.filter(id => id !== categoryId)
      : [...filters.category, categoryId];
    handleFilterChange('category', newCategory);
  };

  const handleTypeToggle = (typeId: string) => {
    const newType = filters.type.includes(typeId)
      ? filters.type.filter(id => id !== typeId)
      : [...filters.type, typeId];
    handleFilterChange('type', newType);
  };

  const clearAllFilters = () => {
    const clearedFilters: ProductFilters = {
      searchTerm: "",
      status: [],
      category: [],
      type: [],
      priceRange: { min: null, max: null },
      hasCommission: null,
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const activeFiltersCount = [
    filters.searchTerm ? 1 : 0,
    filters.status.length,
    filters.category.length,
    filters.type.length,
    filters.priceRange.min !== null || filters.priceRange.max !== null ? 1 : 0,
    filters.hasCommission !== null ? 1 : 0,
  ].reduce((acc, count) => acc + count, 0);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <FilterIcon className="h-4 w-4 mr-2" />
          Filtros
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <FilterIcon className="h-5 w-5" />
            Filtros Avançados
          </SheetTitle>
          <SheetDescription>
            Aplique filtros para encontrar produtos específicos
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Barra de Busca */}
          <div className="space-y-2">
            <Label htmlFor="search">Buscar produtos</Label>
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Nome, descrição..."
                value={filters.searchTerm}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                className="pl-10"
              />
              {filters.searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                  onClick={() => handleFilterChange('searchTerm', '')}
                >
                  <XIcon className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {/* Filtros de Status */}
          <div className="space-y-3">
            <Label>Status</Label>
            <div className="space-y-2">
              {statusOptions.map((status) => {
                const Icon = status.icon;
                return (
                  <div key={status.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status.id}`}
                      checked={filters.status.includes(status.id)}
                      onCheckedChange={() => handleStatusToggle(status.id)}
                    />
                    <Label htmlFor={`status-${status.id}`} className="flex items-center gap-2 cursor-pointer">
                      <Icon className={`h-4 w-4 ${status.color}`} />
                      {status.name}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Filtros de Categoria */}
          <div className="space-y-3">
            <Label>Categorias</Label>
            <div className="space-y-2">
              {categoryOptions.map((category) => (
                <div key={category.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={filters.category.includes(category.id)}
                      onCheckedChange={() => handleCategoryToggle(category.id)}
                    />
                    <Label htmlFor={`category-${category.id}`} className="cursor-pointer">
                      {category.name}
                    </Label>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Filtros de Tipo */}
          <div className="space-y-3">
            <Label>Tipos</Label>
            <div className="space-y-2">
              {typeOptions.map((type) => (
                <div key={type.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type.id}`}
                      checked={filters.type.includes(type.id)}
                      onCheckedChange={() => handleTypeToggle(type.id)}
                    />
                    <Label htmlFor={`type-${type.id}`} className="cursor-pointer">
                      {type.name}
                    </Label>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {type.count}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Filtros de Preço */}
          <div className="space-y-3">
            <Label>Faixa de Preço</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label htmlFor="min-price" className="text-xs">Mínimo</Label>
                <Input
                  id="min-price"
                  type="number"
                  placeholder="0,00"
                  value={filters.priceRange.min || ''}
                  onChange={(e) => handleFilterChange('priceRange', {
                    ...filters.priceRange,
                    min: e.target.value ? parseFloat(e.target.value) : null
                  })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-price" className="text-xs">Máximo</Label>
                <Input
                  id="max-price"
                  type="number"
                  placeholder="999,99"
                  value={filters.priceRange.max || ''}
                  onChange={(e) => handleFilterChange('priceRange', {
                    ...filters.priceRange,
                    max: e.target.value ? parseFloat(e.target.value) : null
                  })}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Filtro de Comissão */}
          <div className="space-y-3">
            <Label>Comissão</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has-commission"
                  checked={filters.hasCommission === true}
                  onCheckedChange={(checked) => handleFilterChange('hasCommission', checked ? true : null)}
                />
                <Label htmlFor="has-commission" className="cursor-pointer">
                  Apenas produtos com comissão
                </Label>
              </div>
            </div>
          </div>

          {/* Botões de Ação */}
          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={clearAllFilters} className="flex-1">
              Limpar Filtros
            </Button>
            <Button onClick={() => setIsOpen(false)} className="flex-1">
              Aplicar Filtros
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
