import "server-only";
import { auth } from "@repo/auth";
import { getInvitationById } from "@repo/database";
import { headers } from "next/headers";
import { cache } from "react";

export const getSession = cache(async () => {
	const headersList = await headers();
	const session = await auth.api.getSession({
		headers: headersList,
		query: {
			disableCookieCache: true,
		},
	});

	return session;
});

export const getActiveOrganization = cache(async (slug: string) => {
	try {
		const headersList = await headers();
		const activeOrganization = await auth.api.getFullOrganization({
			query: {
				organizationSlug: slug,
			},
			headers: headersList,
		});

		return activeOrganization;
	} catch {
		return null;
	}
});

export const getOrganizationList = cache(async () => {
	try {
		const headersList = await headers();
		const organizationList = await auth.api.listOrganizations({
			headers: headersList,
		});

		return organizationList;
	} catch {
		return [];
	}
});

export const getUserAccounts = cache(async () => {
	try {
		const headersList = await headers();
		const userAccounts = await auth.api.listUserAccounts({
			headers: headersList,
		});

		return userAccounts;
	} catch {
		return [];
	}
});

export const getUserPasskeys = cache(async () => {
	try {
		const headersList = await headers();
		const userPasskeys = await auth.api.listPasskeys({
			headers: headersList,
		});

		return userPasskeys;
	} catch {
		return [];
	}
});

export const getInvitation = cache(async (id: string) => {
	try {
		return await getInvitationById(id);
	} catch {
		return null;
	}
});
