'use client';

import React, { useState, useEffect } from 'react';
import { ArrowRight, CreditCard, QrCode, Receipt, CheckCircle, TrendingUp, ShieldCheck, ArrowRightIcon, ReceiptIcon, CreditCardIcon, QrCodeIcon, TrendingUpIcon, CheckCircleIcon, ShieldCheckIcon, PlayIcon, CheckIcon } from "lucide-react";
import { motion } from 'framer-motion';
import { Button } from '@ui/components/button';
import Link from 'next/link';

export function Hero() {
  const [currentFeature, setCurrentFeature] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % 3);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  const features = [
    { icon: QrCodeIcon, title: "PIX Instantâneo", desc: "Pagamentos em segundos", color: "text-green-500", bgColor: "bg-green-500/10" },
    { icon: CreditCardIcon, title: "Cartões", desc: "Todas as bandeiras", color: "text-blue-500", bgColor: "bg-blue-500/10" },
    { icon: ReceiptIcon, title: "Boleto", desc: "Tradicional e confiável", color: "text-orange-500", bgColor: "bg-orange-500/10" }
  ];

  const stats = [
    { value: "99.9%", label: "Uptime", icon: TrendingUpIcon },
    { value: "<2s", label: "Processamento", icon: CheckCircleIcon },
    { value: "PCI DSS", label: "Certificação", icon: ShieldCheckIcon }
  ];

  return (
    <section className="relative pt-20 min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-green-50/30 dark:from-slate-950 dark:via-blue-950/30 dark:to-green-950/30">
      {/* Enhanced Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -right-32 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-green-400/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-1/2 -left-32 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
        <motion.div
          className="absolute bottom-0 right-1/4 w-64 h-64 bg-gradient-to-br from-orange-400/20 to-pink-400/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
        />
      </div>

      {/* Enhanced Floating Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-20 left-10"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div className="p-4 rounded-2xl bg-green-500/10 backdrop-blur-sm border border-green-500/20 shadow-lg">
            <QrCodeIcon className="w-8 h-8 text-green-500" />
          </div>
        </motion.div>
        <motion.div
          className="absolute top-1/3 right-16"
          animate={{
            y: [0, -15, 0],
            rotate: [0, -5, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        >
          <div className="p-4 rounded-2xl bg-blue-500/10 backdrop-blur-sm border border-blue-500/20 shadow-lg">
            <CreditCardIcon className="w-8 h-8 text-blue-500" />
          </div>
        </motion.div>
        <motion.div
          className="absolute bottom-1/3 left-20"
          animate={{
            y: [0, -25, 0],
            rotate: [0, 3, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        >
          <div className="p-4 rounded-2xl bg-orange-500/10 backdrop-blur-sm border border-orange-500/20 shadow-lg">
            <ReceiptIcon className="w-8 h-8 text-orange-500" />
          </div>
        </motion.div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <motion.div
          className="mx-auto max-w-6xl"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >

          {/* Status Badge */}
          <motion.div className="text-center mb-8" variants={itemVariants}>
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full border border-green-500/30 bg-green-500/5 backdrop-blur-sm hover:bg-green-500/10 transition-all duration-300">
              <div className="relative">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <div className="absolute inset-0 w-3 h-3 rounded-full bg-green-500 animate-ping opacity-75"></div>
              </div>
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                Gateway de pagamentos para o Brasil
              </span>
            </div>
          </motion.div>

          {/* Main Heading */}
          <motion.div className="text-center mb-12" variants={itemVariants}>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black tracking-tight mb-8 leading-none">
              <span className="block mb-2 bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                Receba pagamentos
              </span>
              <span className="block bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 bg-clip-text text-transparent">
                sem complicação
              </span>
            </h1>

            <p className="mx-auto max-w-3xl text-xl md:text-2xl text-gray-600 dark:text-gray-400 leading-relaxed font-light">
              A plataforma de pagamentos mais completa do Brasil. PIX instantâneo, 
              cartões nacionais e internacionais, boleto bancário. 
              <span className="font-semibold text-blue-500 dark:text-blue-400">Integração em 24h</span>.
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-8" variants={itemVariants}>
            <Button
              size="lg"
              className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-2xl text-lg hover:scale-105 transform transition-all duration-300 shadow-xl hover:shadow-2xl border-none"
              asChild
            >
              <Link href="/auth/register">
                <span>Começar grátis agora</span>
                <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-semibold rounded-2xl text-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 backdrop-blur-sm"
              asChild
            >
              <Link href="/docs">
                <PlayIcon className="w-5 h-5 mr-2" />
                Ver demonstração
              </Link>
            </Button>
          </motion.div>
          
          <motion.div
            className="flex flex-wrap justify-center items-center gap-6 text-sm text-slate-500 dark:text-slate-400 mb-16"
            variants={itemVariants}
          >
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-500" />
              <span>Sem taxa de setup</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-500" />
              <span>Integração em 24h</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-500" />
              <span>Suporte 24/7</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-500" />
              <span>Certificação PCI DSS</span>
            </div>
          </motion.div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className={`text-center p-6 rounded-3xl border border-slate-200 dark:border-slate-800 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 delay-${index * 100}`}>
                <div className="inline-flex p-3 rounded-full bg-gradient-to-r from-blue-500/10 to-green-500/10 mb-4">
                  <stat.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-3xl font-black text-slate-900 dark:text-slate-100 mb-2">{stat.value}</div>
                <div className="text-sm font-medium text-slate-600 dark:text-slate-400">{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Payment Methods - Interactive */}
          <div className="mb-16">
            <p className="text-center text-sm font-medium text-gray-600 dark:text-gray-400 mb-8">
              Métodos de pagamento suportados
            </p>
            <div className="flex flex-wrap items-center justify-center gap-6">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                const isActive = currentFeature === index;
                return (
                  <div
                    key={index}
                    className={`flex items-center gap-4 px-8 py-4 rounded-2xl border backdrop-blur-sm transition-all duration-500 cursor-pointer hover:scale-110 ${
                      isActive
                        ? `${feature.bgColor} border-current ${feature.color} scale-110 shadow-lg`
                        : 'bg-white/50 dark:bg-slate-900/50 border-slate-200 dark:border-slate-700 hover:bg-white/80'
                    }`}
                    onClick={() => setCurrentFeature(index)}
                  >
                    <Icon className={`w-8 h-8 transition-all duration-300 ${isActive ? feature.color : 'text-slate-600 dark:text-slate-400'}`} />
                    <div>
                      <div className={`font-bold text-lg ${isActive ? 'text-slate-900 dark:text-slate-100' : 'text-slate-700 dark:text-slate-300'}`}>
                        {feature.title}
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-500">{feature.desc}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Main Feature Card */}
          <div className="relative mx-auto max-w-6xl">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-green-600/20 blur-3xl rounded-3xl"></div>
            <div className="relative rounded-3xl border border-white/20 bg-white/10 dark:bg-slate-900/20 backdrop-blur-xl p-8 md:p-12">
              <div className="grid gap-12 md:grid-cols-3">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div key={index} className="text-center group hover:scale-105 transform transition-all duration-300">
                      <div className={`mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl ${feature.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className={`h-8 w-8 ${feature.color}`} />
                      </div>
                      <h3 className="mb-4 text-xl font-bold text-gray-900 dark:text-gray-100">{feature.title}</h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        {index === 0 && "Receba pagamentos instantâneos com PIX, disponível 24 horas por dia, 7 dias por semana"}
                        {index === 1 && "Aceite Visa, Mastercard, Elo e todas as principais bandeiras brasileiras"}
                        {index === 2 && "Método tradicional e confiável, aceito em todos os bancos do Brasil"}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Payment Technologies */}
          <motion.div className="mt-20 text-center" variants={itemVariants}>
            <h5 className="mb-8 text-sm font-medium text-gray-600 dark:text-gray-400">
              Tecnologias de pagamento suportadas
            </h5>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 max-w-4xl mx-auto">
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-lg">PIX</span>
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">PIX Instant</span>
              </div>
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <CreditCardIcon className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Cartões</span>
              </div>
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                  <ReceiptIcon className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Boleto</span>
              </div>
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <ShieldCheckIcon className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">PCI DSS</span>
              </div>
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">API</span>
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">REST API</span>
              </div>
              <div className="flex flex-col items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">SDK</span>
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Multi SDK</span>
              </div>
            </div>
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Infraestrutura robusta e segura, certificada pelos principais órgãos reguladores brasileiros.
                <span className="font-medium text-gray-700 dark:text-gray-300"> Compliance total com LGPD e Banco Central.</span>
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
