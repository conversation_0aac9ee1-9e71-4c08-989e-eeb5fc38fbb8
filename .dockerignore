# Docker
Dockerfile*
.dockerignore

# Dependências
node_modules
**/node_modules

# Build outputs
.next
**/.next
dist
**/dist
out
**/out

# Arquivos de desenvolvimento
.env*
!.env.example

# Git
.git
.gitignore

# Documentação
README.md
**/README.md
docs/
**/docs/
_docs/
**/_docs/

# Testes
coverage/
**/coverage/
.nyc_output
**/.nyc_output
test-results/
playwright-report/
playwright/.cache/
tests/
**/tests/

# Logs
logs/
**/logs/
*.log

# Cache
.cache/
**/.cache/
.turbo/
**/.turbo/

# IDEs
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Outros
*.tsbuildinfo
.eslintcache

# Manter arquivos essenciais para o build
!pnpm-lock.yaml
!pnpm-workspace.yaml
!package.json
!turbo.json
!config/
!packages/
!tooling/
!apps/
