import { config } from "@repo/config";

interface ChatwootConfig {
  baseUrl: string;
  accountId: string;
  apiAccessToken: string;
}

interface ConversationFilters {
  page: number;
  limit: number;
  status?: string;
  channel?: string;
  accountId: string;
}

interface CreateConversationData {
  contactId: string;
  message?: string;
  channel: "whatsapp" | "sms" | "email";
  source?: string;
  accountId: string;
  agentId: string;
}

interface UpdateConversationData {
  status?: "open" | "pending" | "resolved" | "closed";
  priority?: "low" | "medium" | "high" | "urgent";
  assigneeId?: string;
  tags?: string[];
}

interface ChatwootConversation {
  id: string;
  inbox_id: number;
  conversation_id: number;
  status: string;
  priority: string;
  assignee_id?: number;
  tags: string[];
  created_at: string;
  updated_at: string;
  contact: {
    id: number;
    name: string;
    email?: string;
    phone_number?: string;
    avatar_url?: string;
  };
  messages: Array<{
    id: number;
    content: string;
    message_type: number;
    created_at: string;
    sender: {
      id: number;
      name: string;
      type: string;
    };
  }>;
}

export class ChatwootService {
  private config: ChatwootConfig;

  constructor() {
    this.config = {
      baseUrl: config.chatwoot?.baseUrl || process.env.CHATWOOT_BASE_URL || "",
      accountId: config.chatwoot?.accountId || process.env.CHATWOOT_ACCOUNT_ID || "",
      apiAccessToken: config.chatwoot?.apiAccessToken || process.env.CHATWOOT_API_ACCESS_TOKEN || "",
    };

    if (!this.config.baseUrl || !this.config.accountId || !this.config.apiAccessToken) {
      throw new Error("Configuração do Chatwoot incompleta");
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.baseUrl}/api/v1/accounts/${this.config.accountId}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        "api_access_token": this.config.apiAccessToken,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Chatwoot API error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  async listConversations(filters: ConversationFilters): Promise<ChatwootConversation[]> {
    const queryParams = new URLSearchParams({
      page: filters.page.toString(),
      per_page: filters.limit.toString(),
    });

    if (filters.status) {
      queryParams.append("status", filters.status);
    }

    if (filters.channel) {
      queryParams.append("inbox_id", this.getInboxIdByChannel(filters.channel));
    }

    return this.makeRequest<ChatwootConversation[]>(`/conversations?${queryParams}`);
  }

  async getConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}`);
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async createConversation(data: CreateConversationData): Promise<ChatwootConversation> {
    const payload = {
      contact_id: data.contactId,
      inbox_id: this.getInboxIdByChannel(data.channel),
      source: data.source || "api",
      message: data.message,
      assignee_id: data.agentId,
    };

    return this.makeRequest<ChatwootConversation>("/conversations", {
      method: "POST",
      body: JSON.stringify(payload),
    });
  }

  async updateConversation(
    id: string,
    data: UpdateConversationData,
    accountId: string
  ): Promise<ChatwootConversation | null> {
    const payload: any = {};

    if (data.status) {
      payload.status = data.status;
    }

    if (data.priority) {
      payload.priority = data.priority;
    }

    if (data.assigneeId) {
      payload.assignee_id = data.assigneeId;
    }

    if (data.tags) {
      payload.tags = data.tags;
    }

    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}`, {
        method: "PATCH",
        body: JSON.stringify(payload),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async closeConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_status`, {
        method: "POST",
        body: JSON.stringify({ status: "closed" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async reopenConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_status`, {
        method: "POST",
        body: JSON.stringify({ status: "open" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async assignConversation(
    id: string,
    assigneeId: string,
    accountId: string
  ): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/assign`, {
        method: "POST",
        body: JSON.stringify({ assignee_id: assigneeId }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async starConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_priority`, {
        method: "POST",
        body: JSON.stringify({ priority: "urgent" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async unstarConversation(id: string, accountId: string): Promise<ChatwootConversation | null> {
    try {
      return await this.makeRequest<ChatwootConversation>(`/conversations/${id}/toggle_priority`, {
        method: "POST",
        body: JSON.stringify({ priority: "low" }),
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  async sendMessage(
    conversationId: string,
    message: string,
    messageType: "incoming" | "outgoing" = "outgoing"
  ): Promise<any> {
    const payload = {
      content: message,
      message_type: messageType === "outgoing" ? 0 : 1, // 0 = outgoing, 1 = incoming
    };

    return this.makeRequest(`/conversations/${conversationId}/messages`, {
      method: "POST",
      body: JSON.stringify(payload),
    });
  }

  private getInboxIdByChannel(channel: string): string {
    // Mapeamento de canais para inbox IDs do Chatwoot
    const channelMap: Record<string, string> = {
      whatsapp: process.env.CHATWOOT_WHATSAPP_INBOX_ID || "1",
      sms: process.env.CHATWOOT_SMS_INBOX_ID || "2",
      email: process.env.CHATWOOT_EMAIL_INBOX_ID || "3",
    };

    return channelMap[channel] || "1";
  }

  // Método para sincronizar dados do Chatwoot com o banco local
  async syncConversations(accountId: string): Promise<void> {
    try {
      const conversations = await this.listConversations({
        page: 1,
        limit: 100,
        accountId,
      });

      // Aqui você implementaria a lógica para sincronizar com seu banco de dados
      // Por exemplo, usando o Drizzle para inserir/atualizar registros
      console.log(`Sincronizando ${conversations.length} conversas`);

      // TODO: Implementar sincronização com banco local

    } catch (error) {
      console.error("Erro ao sincronizar conversas:", error);
      throw error;
    }
  }
}

// Instância singleton do serviço
export const chatwootService = new ChatwootService();
