@theme {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-success: var(--success);
	--color-success-foreground: var(--success-foreground);
	--color-warning: var(--warning);
	--color-warning-foreground: var(--warning-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-highlight: var(--highlight);
	--color-highlight-foreground: var(--highlight-foreground);
	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--font-sans: var(--font-geist-sans);

	--animation-accordion-down: accordion-down 0.2s ease-out;
	--animation-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}
		to {
			height: var(--radix-accordion-content-height);
		}
	}
	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}
		to {
			height: 0;
		}
	}
}

@layer base {
	:root {
		--border: hsl(231, 10%, 88%);
		--input: hsl(231, 10%, 88%);
		--ring: #0062FF;
		--background: hsl(231, 10%, 98%);
		--foreground: hsl(233, 10%, 10%);
		--primary: #0062FF;
		--primary-foreground: #ffffff;
		--secondary: hsl(231, 10%, 94%);
		--secondary-foreground: hsl(233, 10%, 10%);
		--destructive: #ef4444;
		--destructive-foreground: #ffffff;
		--success: #10b981;
		--success-foreground: #ffffff;
		--warning: #f59e0b;
		--warning-foreground: #ffffff;
		--muted: hsl(231, 10%, 96.5%);
		--muted-foreground: hsl(231, 10%, 60%);
		--accent: hsl(231, 10%, 94%);
		--accent-foreground: hsl(233, 10%, 10%);
		--popover: #ffffff;
		--popover-foreground: hsl(233, 10%, 10%);
		--card: #ffffff;
		--card-foreground: hsl(233, 10%, 10%);
		--highlight: #0062FF;
		--highlight-foreground: #ffffff;
		--radius: 0.5rem;

		/* Polar System Colors */
		--polar-50: hsl(233, 5%, 85%);
		--polar-100: hsl(233, 5%, 79%);
		--polar-200: hsl(233, 5%, 68%);
		--polar-300: hsl(233, 5%, 62%);
		--polar-400: hsl(233, 5%, 52%);
		--polar-500: hsl(233, 5%, 46%);
		--polar-600: hsl(233, 5%, 24%);
		--polar-700: hsl(233, 5%, 12%);
		--polar-800: hsl(233, 5%, 9.5%);
		--polar-900: hsl(233, 5%, 6.5%);
		--polar-950: hsl(233, 5%, 3%);

		/* Blue System Colors */
		--blue-50: #E5EFFF;
		--blue-100: #CCE0FF;
		--blue-200: #99C0FF;
		--blue-300: #66A1FF;
		--blue-400: #3381FF;
		--blue-500: #0062FF;
		--blue-600: #0054DB;
		--blue-700: #0047B8;
		--blue-800: #003994;
		--blue-900: #002B70;
		--blue-950: #00245E;

		/* Gray System Colors */
		--gray-50: hsl(231, 10%, 98%);
		--gray-100: hsl(233, 10%, 96.5%);
		--gray-200: hsl(231, 10%, 94%);
		--gray-300: hsl(231, 10%, 88%);
		--gray-400: hsl(231, 10%, 60%);
		--gray-500: hsl(233, 10%, 40%);
		--gray-600: hsl(233, 10%, 30%);
		--gray-700: hsl(233, 10%, 20%);
		--gray-800: hsl(233, 10%, 10%);
		--gray-900: hsl(233, 10%, 5%);
		--gray-950: hsl(233, 10%, 0%);

		/* fumadocs */
		--fd-banner-height: 4.5rem;
	}

	.dark {
		--border: hsl(233, 10%, 20%);
		--input: hsl(233, 10%, 20%);
		--ring: #0062FF;
		--background: hsl(233, 10%, 5%);
		--foreground: hsl(231, 10%, 98%);
		--primary: #0062FF;
		--primary-foreground: #ffffff;
		--secondary: hsl(233, 10%, 10%);
		--secondary-foreground: hsl(231, 10%, 98%);
		--destructive: #ef4444;
		--destructive-foreground: #ffffff;
		--success: #10b981;
		--success-foreground: #ffffff;
		--warning: #f59e0b;
		--warning-foreground: #ffffff;
		--muted: hsl(233, 10%, 10%);
		--muted-foreground: hsl(231, 10%, 60%);
		--accent: hsl(233, 10%, 10%);
		--accent-foreground: hsl(231, 10%, 98%);
		--popover: hsl(233, 10%, 10%);
		--popover-foreground: hsl(231, 10%, 98%);
		--card: hsl(233, 10%, 10%);
		--card-foreground: hsl(231, 10%, 98%);
		--highlight: #0062FF;
		--highlight-foreground: #ffffff;

		/* Polar System Colors - Dark */
		--polar-50: hsl(233, 5%, 3%);
		--polar-100: hsl(233, 5%, 6.5%);
		--polar-200: hsl(233, 5%, 9.5%);
		--polar-300: hsl(233, 5%, 12%);
		--polar-400: hsl(233, 5%, 24%);
		--polar-500: hsl(233, 5%, 46%);
		--polar-600: hsl(233, 5%, 52%);
		--polar-700: hsl(233, 5%, 62%);
		--polar-800: hsl(233, 5%, 68%);
		--polar-900: hsl(233, 5%, 79%);
		--polar-950: hsl(233, 5%, 85%);

		/* Blue System Colors - Dark */
		--blue-50: #00245E;
		--blue-100: #002B70;
		--blue-200: #003994;
		--blue-300: #0047B8;
		--blue-400: #0054DB;
		--blue-500: #0062FF;
		--blue-600: #3381FF;
		--blue-700: #66A1FF;
		--blue-800: #99C0FF;
		--blue-900: #CCE0FF;
		--blue-950: #E5EFFF;

		/* Gray System Colors - Dark */
		--gray-50: hsl(233, 10%, 0%);
		--gray-100: hsl(233, 10%, 5%);
		--gray-200: hsl(233, 10%, 10%);
		--gray-300: hsl(233, 10%, 20%);
		--gray-400: hsl(233, 10%, 30%);
		--gray-500: hsl(233, 10%, 40%);
		--gray-600: hsl(233, 10%, 60%);
		--gray-700: hsl(231, 10%, 88%);
		--gray-800: hsl(231, 10%, 94%);
		--gray-900: hsl(233, 10%, 96.5%);
		--gray-950: hsl(231, 10%, 98%);
	}

	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		@apply border-border;
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 1.5rem;
	width: 100%;
	max-width: var(--container-7xl);
}

@utility no-scrollbar {
	&::-webkit-scrollbar {
		display: none;
	}
	-ms-overflow-style: none;
	scrollbar-width: none;
}
