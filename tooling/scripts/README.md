# Scripts de Desenvolvimento

Este diretório contém scripts úteis para desenvolvimento e administração da aplicação.

## Scripts Disponíveis

### 1. <PERSON><PERSON><PERSON> <PERSON> (`create:user`)

Cria um novo usuário com diferentes roles e opcionalmente uma organização.

```bash
pnpm --filter scripts create:user
```

**Funcionalidades:**
- Criação de usuários com diferentes roles (USER, TEACHER, AFFILIATE, ADMIN, SUPER_ADMIN)
- Criação automática de perfis específicos (TeacherProfile, AffiliateProfile)
- Opção de criar uma organização associada ao usuário
- Geração automática de senha segura

**Exemplo de uso:**
```bash
pnpm --filter scripts create:user
# Seguir as instruções interativas
```

### 2. Seed do <PERSON> (`seed:db`)

Popula o banco de dados com dados de exemplo para desenvolvimento.

```bash
pnpm --filter scripts seed:db
```

**O que é criado:**
- 1 usuário Super Admin (<EMAIL> / admin123)
- 1 organização "Super Gateway Academy"
- 3 usuários de exemplo:
  - Professor (<EMAIL> / teacher123)
  - Afiliado (<EMAIL> / affiliate123)
  - Cliente (<EMAIL> / user123)
- 4 categorias de produtos
- 3 produtos de exemplo (cursos e e-books)
- 2 cupons de desconto
- Perfis e relacionamentos apropriados

### 3. Testar Seed (`test:seed`)

Verifica se todos os dados do seed foram criados corretamente.

```bash
pnpm --filter scripts test:seed
```

**Funcionalidades:**
- Conta todos os usuários, organizações, produtos e categorias
- Verifica se os perfis específicos foram criados
- Mostra detalhes dos dados criados
- Valida a integridade dos relacionamentos

### 4. Corrigir Perfis (`fix:profiles`)

Verifica e corrige perfis específicos que podem não ter sido criados.

```bash
pnpm --filter scripts fix:profiles
```

**Funcionalidades:**
- Verifica usuários TEACHER e AFFILIATE
- Cria perfis TeacherProfile e AffiliateProfile se necessário
- Mostra estatísticas dos perfis existentes
- Corrige problemas de sincronização

### 5. Reset do Banco (`reset:db`)

Remove todos os dados do banco e opcionalmente executa o seed novamente.

```bash
pnpm --filter scripts reset:db
```

**⚠️ ATENÇÃO:** Este comando remove TODOS os dados do banco de dados!

**Funcionalidades:**
- Confirmação obrigatória (digite "RESET")
- Remove todos os dados respeitando foreign keys
- Opção de executar seed automaticamente após reset

## Credenciais Padrão (após seed)

### Super Admin
- **Email:** <EMAIL>
- **Senha:** admin123
- **Role:** SUPER_ADMIN

### Professor
- **Email:** <EMAIL>
- **Senha:** teacher123
- **Role:** TEACHER

### Afiliado
- **Email:** <EMAIL>
- **Senha:** affiliate123
- **Role:** AFFILIATE

### Cliente
- **Email:** <EMAIL>
- **Senha:** user123
- **Role:** USER

## Requisitos

- Banco de dados configurado e migrations aplicadas
- Arquivo `.env` configurado na raiz do projeto
- Dependências instaladas (`pnpm install`)

## Estrutura dos Dados Criados

### Organização
- Nome: "Super Gateway Academy"
- Slug: "super-gateway-academy"
- Moeda: BRL
- Timezone: America/Sao_Paulo
- Plano: Premium (ativo)

### Categorias
1. **Programação** (💻) - Cursos de desenvolvimento de software
2. **Design** (🎨) - Cursos de design gráfico e UX/UI
3. **Marketing Digital** (📱) - Estratégias de marketing online
4. **E-books** (📚) - Livros digitais educativos

### Produtos
1. **React do Zero ao Avançado** - Curso (R$ 299,00)
2. **Design System Completo** - Curso (R$ 199,00)
3. **Marketing Digital 2024** - E-book (R$ 49,00)

### Cupons
- **BEMVINDO10** - 10% de desconto (mín. R$ 50,00)
- **DESCONTO50** - R$ 50,00 de desconto (mín. R$ 100,00)

## Fluxo de Desenvolvimento Recomendado

1. **Primeira execução:**
   ```bash
   pnpm --filter scripts seed:db
   ```

2. **Verificar dados:**
   ```bash
   pnpm --filter scripts test:seed
   ```

3. **Se houver problemas com perfis:**
   ```bash
   pnpm --filter scripts fix:profiles
   ```

4. **Para desenvolvimento contínuo:**
   ```bash
   pnpm --filter scripts create:user
   ```

5. **Se precisar recomeçar:**
   ```bash
   pnpm --filter scripts reset:db
   ```

## Desenvolvimento

Para modificar os scripts ou adicionar novos dados ao seed, edite os arquivos em `src/`:

- `create-user.ts` - Script de criação de usuários
- `seed.ts` - Script de seed principal
- `test-seed.ts` - Script de teste dos dados
- `fix-profiles.ts` - Script de correção de perfis
- `reset-db.ts` - Script de reset do banco

## Troubleshooting

### Erro de permissões no banco
Verifique se o usuário do banco tem permissões para criar/deletar dados.

### Erro de foreign key
O script de reset deleta os dados na ordem correta. Se houver erro, pode ser necessário atualizar a ordem no `reset-db.ts`.

### Dados duplicados
Os scripts verificam se os dados já existem antes de criar, então é seguro executar múltiplas vezes.

### Perfis não criados
Use o comando `fix:profiles` para corrigir perfis que podem não ter sido criados durante o seed.

### Problemas de sincronização
Execute `pnpm --filter database push` para sincronizar o banco com o schema Prisma.
