#!/bin/bash

# Script para testar build do Docker localmente
# Uso: ./test-docker-build.sh [DOCKERFILE_NAME]

set -e

# Configurações
DOCKERFILE_NAME=${1:-"Dockerfile.cloud-run"}
IMAGE_NAME="super-gateway-test"
CONTAINER_NAME="super-gateway-test-container"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testando build do Docker localmente${NC}"
echo -e "${BLUE}Dockerfile: ${DOCKERFILE_NAME}${NC}"
echo ""

# Verificar se Docker está rodando
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker não está rodando${NC}"
    exit 1
fi

# Verificar se o Dockerfile existe
if [ ! -f "apps/web/${DOCKERFILE_NAME}" ]; then
    echo -e "${RED}❌ Dockerfile não encontrado: apps/web/${DOCKERFILE_NAME}${NC}"
    echo "Dockerfiles disponíveis:"
    ls -la apps/web/Dockerfile*
    exit 1
fi

# Verificar se os arquivos essenciais existem
echo -e "${BLUE}🔍 Verificando arquivos essenciais...${NC}"
REQUIRED_FILES=("pnpm-lock.yaml" "pnpm-workspace.yaml" "package.json" "turbo.json")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ Arquivo obrigatório não encontrado: $file${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ $file${NC}"
done

# Limpar containers e imagens anteriores
echo -e "${BLUE}🧹 Limpando containers e imagens anteriores...${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true
docker rmi $IMAGE_NAME 2>/dev/null || true

# Build da imagem
echo -e "${BLUE}🏗️  Fazendo build da imagem...${NC}"
echo -e "${BLUE}📁 Contexto: $(pwd)${NC}"
echo -e "${BLUE}🐳 Dockerfile: apps/web/${DOCKERFILE_NAME}${NC}"

if docker build -f apps/web/${DOCKERFILE_NAME} -t $IMAGE_NAME .; then
    echo -e "${GREEN}✅ Build concluído com sucesso!${NC}"
else
    echo -e "${RED}❌ Build falhou${NC}"
    exit 1
fi

# Testar execução do container
echo -e "${BLUE}🚀 Testando execução do container...${NC}"
docker run -d --name $CONTAINER_NAME -p 3000:3000 $IMAGE_NAME

# Aguardar container inicializar
echo -e "${BLUE}⏳ Aguardando container inicializar...${NC}"
sleep 10

# Verificar se container está rodando
if docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${GREEN}✅ Container está rodando${NC}"
else
    echo -e "${RED}❌ Container não está rodando${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Testar health check
echo -e "${BLUE}🏥 Testando health check...${NC}"
if curl -f http://localhost:3000/api/health &>/dev/null; then
    echo -e "${GREEN}✅ Health check passou${NC}"
else
    echo -e "${YELLOW}⚠️  Health check falhou (pode ser normal se não houver endpoint /api/health)${NC}"
fi

# Mostrar logs do container
echo -e "${BLUE}📋 Logs do container:${NC}"
docker logs $CONTAINER_NAME --tail 20

# Parar e limpar
echo -e "${BLUE}🧹 Limpando...${NC}"
docker stop $CONTAINER_NAME
docker rm $CONTAINER_NAME
docker rmi $IMAGE_NAME

echo ""
echo -e "${GREEN}✅ Teste concluído com sucesso!${NC}"
echo -e "${GREEN}🎉 O Dockerfile está funcionando corretamente${NC}"
echo ""
echo -e "${BLUE}💡 Agora você pode fazer deploy no Google Cloud Run:${NC}"
echo "./deploy-cloud-run.sh [PROJECT_ID] [REGION] [SERVICE_NAME]"
