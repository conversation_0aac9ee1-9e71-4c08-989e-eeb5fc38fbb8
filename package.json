{"name": "supgateway", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint --assist-enabled . ", "check": "biome check", "clean": "turbo clean", "format": "biome format . --write"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "2.1.0", "@repo/tsconfig": "workspace:*", "@types/node": "^24.0.10", "dotenv-cli": "^8.0.0", "turbo": "^2.5.4", "typescript": "5.8.3"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0"}}, "dependencies": {"decimal.js": "^10.6.0"}}